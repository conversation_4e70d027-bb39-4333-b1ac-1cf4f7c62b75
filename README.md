# Nearby Connect 🌍

A location-based social networking app to discover, connect, and engage with people, events, and deals around you.

Built with modern web technologies for a fast, responsive, and accessible experience.

---

## 🚀 Live Demo
👉 [View Live on vercel.](https://nearbyconnect-v004.vercel.app)

---

## 🛠️ Tech Stack


### Frontend
- **React** + **TypeScript**
- **Vite** with SWC (`@vitejs/plugin-react-swc`)
- **Tailwind CSS** + **shadcn/ui** (Radix UI)
- **Lucide Icons**, **TanStack React Query**

### Backend & DB
- **Supabase** (PostgreSQL, Auth, Edge Functions)

### Mobile
- **Capacitor** for native builds (Android/iOS)

---

## 📁 Project Structure


```txt
src/
├── components/        # Shared UI components (Radix primitives)
├── hooks/             # Custom React hooks
├── integrations/
│   └── supabase/      # Supabase client and DB types
├── lib/               # Utilities and helpers
├── pages/             # Route-based components (Index, Auth, Profile, etc.)
└── styles/            # Tailwind theming
public/                # Static assets
supabase/              # Supabase functions and migrations
android/               # Android native project (Capacitor)
```

---

## ✨ Features

* 🔐 **Authentication** with Supabase
* 📍 **Location-based** filtering of users, posts, and events
* 🧑‍💼 **User Profiles** with editable interests and visibility controls
* 🧭 **Nearby Connections** and messaging
* 📱 **Responsive Design** with safe-area mobile UI
* 🎨 **Light/Dark mode** with custom themes
  
---

## 🎯 MVP Feature Map

Nearby Connect’s MVP is designed to solve the core problem: helping users find and chat with nearby people who share interests. Here’s what’s included:

1. **Local Discovery Core**  
   - Search for nearby users (within a customizable 5,000-meter radius)
   - Filter by interest tags
   - View basic profiles: name, photo, shared interests

2. **Connection Requests**  
   - Send and accept connection requests
   - Optionally include a short “why connect?” message

3. **Basic Chat**  
   - Simple, reliable text messaging between connected users

4. **User Profiles**  
   - Minimal profiles: name, photo, short bio, interest tags

5. **Onboarding Flow**  
   - Quick signup
   - One-minute setup: pick interests, enable location sharing
   - Easy invite link to refer friends

These features keep the MVP lean, focused, and ready for rapid iteration—delivering real value from day one.

---

## 📦 Scripts


```bash
npm install       # Install dependencies
npm run dev       # Run Vite dev server at http://localhost:8080
npm run build     # Build for production
npm run lint      # Run ESLint
```

---

## 📱 Mobile

To build and run the mobile app (Android):

```bash
npm run build
npx cap sync android
npx cap open android
```

See [`android/`](android/) and [`capacitor.config.ts`](capacitor.config.ts).

---

## 🧪 Testing

Add instructions here if you use tests. Example:

```bash
npm run test
```

---

## 🤝 Contributing

Pull requests and issues welcome! Please follow the coding standards and check linting before submitting.

---

## 📝 License

Specify your license here (e.g., MIT).

---

## 🛠️ Troubleshooting

Common issues and solutions can be documented here as the project evolves.

---

## ⚙️ Configuration

* Vite aliases `@` → `src/`
* Environment config pulled from `.env` and Supabase CLI
* Hosted and deployed on [Vercel](https://vercel.com/)

---

## ✅ Code Quality

* **ESLint** configured for TypeScript & React
* **Modular architecture** with clear separation of concerns
* **Accessibility-first** design using Radix UI components

---

## 📌 Status

> This is a **working prototype** used for testing product ideas and full-stack workflows.
> It's part of a broader initiative to build inclusive, high-utility apps for hyperlocal engagement.

---

## 🧠 Author

**Vineet Rawat**
Founder & Product Engineer, [Rawat Innovations Pvt. Ltd.](https://rawatinnovations.com)

📫 [LinkedIn](https://linkedin.com/in/vineetrawatdev) | [GitHub](https://github.com/vineetrawat)
