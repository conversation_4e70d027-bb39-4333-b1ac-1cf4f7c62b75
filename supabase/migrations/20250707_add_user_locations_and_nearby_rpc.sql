-- Enable PostGIS extension
create extension if not exists postgis;

-- User locations table
create table if not exists user_locations (
  user_id uuid references auth.users primary key,
  location geography(Point, 4326),
  updated_at timestamp default now()
);

-- RPC for nearby users
drop function if exists get_users_nearby;
create or replace function get_users_nearby(lat double precision, lng double precision, radius integer)
returns table (
  user_id uuid,
  lat double precision,
  lng double precision,
  distance double precision
)
as $$
begin
  return query
  select
    u.user_id,
    ST_Y(u.location::geometry) as lat,
    ST_X(u.location::geometry) as lng,
    ST_Distance(u.location, ST_MakePoint(lng, lat)::geography) as distance
  from user_locations u
  where ST_DWithin(u.location, ST_MakePoint(lng, lat)::geography, radius);
end;
$$ language plpgsql;
