-- <PERSON>reate function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (
    user_id,
    full_name,
    username,
    location_lat,
    location_lng,
    is_visible,
    visibility_radius
  )
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'username', ''),
    NULL, -- Will be set during onboarding
    NULL, -- Will be set during onboarding
    true,
    5000 -- Default 5km radius
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create profiles for new users
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_user();

-- Create function to find nearby users based on location
CREATE OR REPLACE FUNCTION public.find_nearby_users(
  user_lat NUMERIC,
  user_lng NUMERIC,
  radius_km INTEGER DEFAULT 5
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  full_name TEXT,
  username TEXT,
  bio TEXT,
  interests TEXT[],
  is_business BOOLEAN,
  distance_km NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.user_id,
    p.full_name,
    p.username,
    p.bio,
    p.interests,
    p.is_business,
    -- Calculate distance using Haversine formula
    ROUND(
      (6371 * acos(
        cos(radians(user_lat)) * 
        cos(radians(p.location_lat)) * 
        cos(radians(p.location_lng) - radians(user_lng)) + 
        sin(radians(user_lat)) * 
        sin(radians(p.location_lat))
      ))::numeric, 2
    ) as distance_km
  FROM public.profiles p
  WHERE 
    p.user_id != auth.uid() -- Exclude current user
    AND p.is_visible = true
    AND p.location_lat IS NOT NULL 
    AND p.location_lng IS NOT NULL
    AND (
      6371 * acos(
        cos(radians(user_lat)) * 
        cos(radians(p.location_lat)) * 
        cos(radians(p.location_lng) - radians(user_lng)) + 
        sin(radians(user_lat)) * 
        sin(radians(p.location_lat))
      )
    ) <= COALESCE(p.visibility_radius / 1000.0, radius_km)
  ORDER BY distance_km ASC
  LIMIT 50;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;