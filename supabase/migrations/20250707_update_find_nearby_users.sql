create or replace function find_nearby_users(user_lat double precision, user_lng double precision, radius_km double precision default 1)
returns table (
  id uuid,
  user_id uuid,
  full_name text,
  username text,
  bio text,
  interests text[],
  is_business boolean,
  lat double precision,
  lng double precision,
  distance_km double precision
) as $$
begin
  return query
  select
    p.id,
    p.user_id,
    p.full_name,
    p.username,
    p.bio,
    p.interests,
    p.is_business,
    p.location_lat as lat,
    p.location_lng as lng,
    (6371 * acos(
      cos(radians(user_lat)) * cos(radians(p.location_lat)) *
      cos(radians(p.location_lng) - radians(user_lng)) +
      sin(radians(user_lat)) * sin(radians(p.location_lat))
    )) as distance_km
  from profiles p
  where p.location_lat is not null and p.location_lng is not null
    and (6371 * acos(
      cos(radians(user_lat)) * cos(radians(p.location_lat)) *
      cos(radians(p.location_lng) - radians(user_lng)) +
      sin(radians(user_lat)) * sin(radians(p.location_lat))
    )) <= radius_km;
end;
$$ language plpgsql;
