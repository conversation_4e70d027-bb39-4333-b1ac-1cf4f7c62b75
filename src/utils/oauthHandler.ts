import { App as CapacitorApp } from '@capacitor/app';
import { supabase } from '@/integrations/supabase/client';

export class OAuthHandler {
  private static instance: OAuthHandler;
  private isListening = false;

  static getInstance(): OAuthHandler {
    if (!OAuthHandler.instance) {
      OAuthHandler.instance = new OAuthHandler();
    }
    return OAuthHandler.instance;
  }

  startListening() {
    if (this.isListening) return;
    
    console.log('Starting OAuth deep link listener...');
    this.isListening = true;

    CapacitorApp.addListener('appUrlOpen', async ({ url }) => {
      console.log('Deep link received:', url);
      
      // Handle both custom scheme and web URLs
      if (url.includes('auth') && (url.includes('#') || url.includes('access_token'))) {
        await this.handleOAuthCallback(url);
      }
    });
  }

  stopListening() {
    if (!this.isListening) return;
    
    console.log('Stopping OAuth deep link listener...');
    CapacitorApp.removeAllListeners();
    this.isListening = false;
  }

  private async handleOAuthCallback(url: string) {
    try {
      console.log('Processing OAuth callback:', url);
      
      // Extract tokens from URL
      let fragment = '';
      
      if (url.includes('#')) {
        fragment = url.split('#')[1];
      } else if (url.includes('access_token')) {
        // Handle query parameters
        const urlObj = new URL(url);
        fragment = urlObj.search.substring(1);
      }
      
      if (!fragment) {
        console.error('No OAuth fragment found in URL');
        return;
      }
      
      const params = new URLSearchParams(fragment);
      const accessToken = params.get('access_token');
      const refreshToken = params.get('refresh_token');
      
      if (accessToken) {
        console.log('Setting OAuth session...');
        
        const { data, error } = await supabase.auth.setSession({
          access_token: accessToken,
          refresh_token: refreshToken || '',
        });
        
        if (error) {
          console.error('OAuth session error:', error);
        } else if (data?.session) {
          console.log('OAuth session established successfully');
          // The auth state change will handle navigation
        }
      } else {
        console.error('No access token found in OAuth callback');
      }
    } catch (err) {
      console.error('Error handling OAuth callback:', err);
    }
  }
}

// Initialize the OAuth handler
export const oauthHandler = OAuthHandler.getInstance();
