import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";

export interface UserProfile {
    id: string;
    user_id: string;
    username?: string;
    full_name?: string;
    location_lat?: number;
    location_lng?: number;
}

export function useUserProfile() {
    const { user } = useAuth();
    const [profile, setProfile] = useState<UserProfile | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        async function fetchProfile() {
            if (!user) {
                setProfile(null);
                setLoading(false);
                return;
            }
            const { data, error } = await supabase
                .from("profiles")
                .select("*")
                .eq("user_id", user.id)
                .single();
            if (error) {
                setProfile(null);
            } else {
                setProfile(data);
            }
            setLoading(false);
        }
        fetchProfile();
    }, [user]);

    return { profile, loading };
}
