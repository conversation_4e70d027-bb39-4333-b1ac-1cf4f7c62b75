import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
}

interface UseLocationReturn {
  location: LocationData | null;
  loading: boolean;
  error: string | null;
  requestLocation: () => Promise<LocationData | null>;
  updateUserLocation: (lat: number, lng: number) => Promise<boolean>;
}

export const useLocation = (): UseLocationReturn => {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const requestLocation = async (): Promise<LocationData | null> => {
    setLoading(true);
    setError(null);

    try {
      if (!navigator.geolocation) {
        throw new Error('Geolocation is not supported by this browser');
      }

      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000 // 5 minutes
          }
        );
      });

      const locationData: LocationData = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy
      };

      setLocation(locationData);
      return locationData;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get location';
      setError(errorMessage);
      
      if (errorMessage.includes('denied')) {
        toast({
          title: "Location Access Denied",
          description: "Please enable location access to find people nearby",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Location Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
      
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateUserLocation = async (lat: number, lng: number): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          location_lat: lat,
          location_lng: lng,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', (await supabase.auth.getUser()).data.user?.id);

      if (error) {
        console.error('Error updating user location:', error);
        toast({
          title: "Error",
          description: "Failed to update your location",
          variant: "destructive",
        });
        return false;
      }

      toast({
        title: "Location Updated",
        description: "Your location has been updated successfully",
      });
      return true;
    } catch (error) {
      console.error('Error updating location:', error);
      return false;
    }
  };

  return {
    location,
    loading,
    error,
    requestLocation,
    updateUserLocation
  };
};