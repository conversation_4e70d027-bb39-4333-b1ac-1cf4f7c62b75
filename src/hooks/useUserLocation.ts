import { Geolocation } from '@capacitor/geolocation';

export async function getUserLocation() {
    try {
        // Check if running on web or native
        const isNative = !(typeof window !== 'undefined' && window.navigator);

        if (isNative) {
            // On native (Android/iOS), explicitly request permissions
            const permStatus = await Geolocation.requestPermissions();
            if (
                permStatus.location !== 'granted' &&
                permStatus.coarseLocation !== 'granted'
            ) {
                throw new Error('Location permission denied');
            }
        }
        // On web, Geolocation.getCurrentPosition will prompt the user
        const coords = await Geolocation.getCurrentPosition({ enableHighAccuracy: true });
        return { lat: coords.coords.latitude, lng: coords.coords.longitude };
    } catch (e: unknown) {
        console.error('Geolocation error:', (e as Error).message || e);
        return null;
    }
}
