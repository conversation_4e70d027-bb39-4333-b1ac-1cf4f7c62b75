import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

const CompleteProfile = () => {
    const navigate = useNavigate();
    const [username, setUsername] = useState('');
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);

    const handleUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setUsername(e.target.value);
        setError('');
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setLoading(true);
        // Check uniqueness
        const { data } = await supabase
            .from('profiles')
            .select('id')
            .eq('username', username)
            .maybeSingle();
        if (data) {
            setError('Username already taken');
            setLoading(false);
            return;
        }
        // Get current user
        const { data: userData } = await supabase.auth.getUser();
        const userId = userData?.user?.id;
        if (!userId) {
            setError('User not found');
            setLoading(false);
            return;
        }
        // Update profile
        const { error: updateError } = await supabase
            .from('profiles')
            .update({ username })
            .eq('user_id', userId);
        if (!updateError) {
            navigate('/');
        } else {
            setError('Failed to save username');
        }
        setLoading(false);
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-background p-4">
            <form onSubmit={handleSubmit} className="max-w-sm w-full space-y-6 bg-card p-6 rounded-lg shadow">
                <h2 className="text-xl font-bold mb-2">Complete Your Profile</h2>
                <Label htmlFor="username">Choose a unique username</Label>
                <Input
                    id="username"
                    name="username"
                    type="text"
                    value={username}
                    onChange={handleUsernameChange}
                    required
                    minLength={3}
                    className={error ? 'border-red-500' : ''}
                />
                {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
                <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? 'Saving...' : 'Save Username'}
                </Button>
            </form>
        </div>
    );
};

export default CompleteProfile;
