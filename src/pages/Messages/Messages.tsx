import './Messages.css';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageCircle, ArrowLeft, Users } from 'lucide-react';
import MobileNavigation from '@/components/MobileNavigation';

const Messages = () => {
  const navigate = useNavigate();
  const { user, loading } = useAuth();

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <MessageCircle className="h-12 w-12 text-primary mx-auto pulse-location" />
          <p className="text-muted-foreground">Loading messages...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background pb-20 mobile-safe-area">
      {/* Header */}
      <header className="messages-header bg-card border-b border-border sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button 
                variant="ghost" 
                size="icon"
                onClick={() => navigate('/')}
                className="md:hidden"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <MessageCircle className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-bold">Messages</h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="messages-main container mx-auto px-4 py-6">
        <Card>
          <CardContent className="p-8 text-center">
            <MessageCircle className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">Messages Coming Soon</h3>
            <p className="text-muted-foreground mb-6">
              Private messaging between connected users will be available soon. 
              For now, connect with people and engage with their posts!
            </p>
            <div className="space-y-3">
              <Button 
                onClick={() => navigate('/connections')} 
                className="w-full"
              >
                <Users className="h-4 w-4 mr-2" />
                Find Friends to Message
              </Button>
              <Button 
                variant="outline" 
                onClick={() => navigate('/nearby')} 
                className="w-full"
              >
                Explore Nearby Posts
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>

      <MobileNavigation />
    </div>
  );
};

export default Messages;