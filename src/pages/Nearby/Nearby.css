.nearby-root-container {
    /* disable scroll */
    overflow: hidden;
    /* ensure content is centered */
    display: flex;
    flex-direction: column;
    align-items: center;
    /* full height */
    min-height: 100vh;
    /* background color */
    background-color: var(--background);
    /* padding for mobile safe area */
    padding-bottom: env(safe-area-inset-bottom);

}

header.nearby-header {
    width: 100%;
}

main.nearby-main {
    padding-top: 0rem;
    padding-bottom: 5rem;
    padding-left: 1rem;
    padding-right: 1rem;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    overflow: hidden;
}

.nearby-map {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.card-no-posts {
    padding: 1rem !important;
    text-align: center;
}

.card-no-posts p {
    margin-bottom: .75rem;
}

button.locate-me-button {
    padding: 0.5rem 0.75rem !important;
}
