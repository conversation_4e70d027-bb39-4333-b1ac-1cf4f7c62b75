import './Connections.css';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Users, UserPlus, ArrowLeft, Search, MapPin, Clock } from 'lucide-react';
import MobileNavigation from '@/components/MobileNavigation';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface Profile {
  id: string;
  user_id: string;
  full_name: string;
  username: string;
  bio: string | null;
  interests: string[] | null;
  is_business: boolean | null;
  distance_km?: number;
}

interface Connection {
  id: string;
  status: string;
  created_at: string;
  requester: Profile;
  receiver: Profile;
}

const Connections = () => {
  const navigate = useNavigate();
  const { user, loading } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Profile[]>([]);
  const [nearbyPeople, setNearbyPeople] = useState<Profile[]>([]);
  const [connections, setConnections] = useState<Connection[]>([]);
  const [requests, setRequests] = useState<Connection[]>([]);
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [loadingNearby, setLoadingNearby] = useState(false);

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  useEffect(() => {
    if (user) {
      fetchConnections();
      fetchRequests();
      fetchNearbyPeople();
    }
  }, [user]);

  const fetchNearbyPeople = async () => {
    setLoadingNearby(true);
    try {
      // First, get current user's location
      const { data: userProfile, error: profileError } = await supabase
        .from('profiles')
        .select('location_lat, location_lng')
        .eq('user_id', user?.id)
        .single();

      if (profileError || !userProfile?.location_lat || !userProfile?.location_lng) {
        // If user doesn't have location, show all people
        const { data, error } = await supabase
          .from('profiles')
          .select('id, user_id, full_name, username, bio, interests, is_business')
          .neq('user_id', user?.id)
          .eq('is_visible', true)
          .limit(20);

        if (error) {
          console.error('Error fetching people:', error);
        } else {
          setNearbyPeople(data || []);
        }
        return;
      }

      // Use the location-based search function
      const { data, error } = await supabase
        .rpc('find_nearby_users', {
          user_lat: userProfile.location_lat,
          user_lng: userProfile.location_lng,
          radius_km: 10 // 10km radius
        });

      if (error) {
        console.error('Error fetching nearby people:', error);
        toast({
          title: "Error",
          description: "Failed to find people nearby",
          variant: "destructive",
        });
      } else {
        setNearbyPeople(data || []);
      }
    } catch (error) {
      console.error('Error fetching people:', error);
    } finally {
      setLoadingNearby(false);
    }
  };

  const fetchConnections = async () => {
    try {
      const { data, error } = await supabase
        .from('connections')
        .select(`
          id,
          status,
          created_at,
          requester_id,
          receiver_id
        `)
        .eq('status', 'accepted')
        .or(`requester_id.eq.${user?.id},receiver_id.eq.${user?.id}`);

      if (error) {
        console.error('Error fetching connections:', error);
      } else {
        // For now, set empty connections since we need to fetch profile data separately
        setConnections([]);
      }
    } catch (error) {
      console.error('Error fetching connections:', error);
    }
  };

  const fetchRequests = async () => {
    try {
      const { data: requestsData, error } = await supabase
        .from('connections')
        .select('id, status, created_at, requester_id, receiver_id')
        .eq('status', 'pending')
        .eq('receiver_id', user?.id);

      if (error) {
        console.error('Error fetching requests:', error);
        return;
      }

      // Fetch requester profiles
      const requesterIds = requestsData?.map(req => req.requester_id) || [];
      const { data: profiles, error: profileError } = await supabase
        .from('profiles')
        .select('id, user_id, full_name, username, bio, interests, is_business')
        .in('user_id', requesterIds);

      if (profileError) {
        console.error('Error fetching profiles:', profileError);
        return;
      }

      // Combine data
      const requestsWithProfiles = requestsData?.map(request => ({
        ...request,
        requester: profiles?.find(p => p.user_id === request.requester_id) || {
          id: '',
          user_id: request.requester_id,
          full_name: 'Unknown User',
          username: 'unknown',
          bio: null,
          interests: null,
          is_business: false
        },
        receiver: {
          id: '',
          user_id: user?.id || '',
          full_name: 'You',
          username: 'you',
          bio: null,
          interests: null,
          is_business: false
        }
      })) || [];

      setRequests(requestsWithProfiles);
    } catch (error) {
      console.error('Error fetching requests:', error);
    }
  };

  const searchUsers = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setLoadingSearch(true);
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, user_id, full_name, username, bio, interests, is_business')
        .neq('user_id', user?.id)
        .or(`full_name.ilike.%${query}%,username.ilike.%${query}%`)
        .limit(10);

      if (error) {
        console.error('Error searching users:', error);
      } else {
        setSearchResults(data || []);
      }
    } catch (error) {
      console.error('Error searching users:', error);
    } finally {
      setLoadingSearch(false);
    }
  };

  const sendConnectionRequest = async (receiverId: string) => {
    try {
      const { error } = await supabase
        .from('connections')
        .insert({
          requester_id: user?.id,
          receiver_id: receiverId,
          status: 'pending'
        });

      if (error) {
        console.error('Error sending connection request:', error);
        toast({
          title: "Error",
          description: "Failed to send connection request",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Request Sent",
          description: "Connection request sent successfully",
        });
        // Remove from search results
        setSearchResults(prev => prev.filter(p => p.user_id !== receiverId));
      }
    } catch (error) {
      console.error('Error sending connection request:', error);
    }
  };

  const handleConnectionRequest = async (connectionId: string, action: 'accept' | 'reject') => {
    try {
      const { error } = await supabase
        .from('connections')
        .update({ status: action === 'accept' ? 'accepted' : 'rejected' })
        .eq('id', connectionId);

      if (error) {
        console.error('Error handling connection request:', error);
        toast({
          title: "Error",
          description: "Failed to handle connection request",
          variant: "destructive",
        });
      } else {
        toast({
          title: action === 'accept' ? "Request Accepted" : "Request Rejected",
          description: `Connection request ${action}ed successfully`,
        });
        fetchConnections();
        fetchRequests();
      }
    } catch (error) {
      console.error('Error handling connection request:', error);
    }
  };

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <Users className="h-12 w-12 text-primary mx-auto pulse-location" />
          <p className="text-muted-foreground">Loading connections...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background pb-20 mobile-safe-area">
      {/* Header */}
      <header className="bg-card border-b border-border sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button 
                variant="ghost" 
                size="icon"
                onClick={() => navigate('/')}
                className="md:hidden"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <Users className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-bold">Connections</h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="connections-main container mx-auto px-4 py-6">
        <Tabs defaultValue="connections" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="connections">Connections ({connections.length})</TabsTrigger>
            <TabsTrigger value="requests">Requests ({requests.length})</TabsTrigger>
            <TabsTrigger value="search">Find People</TabsTrigger>
          </TabsList>

          <TabsContent value="connections" className="space-y-4">
            {connections.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No connections yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Start connecting with people around you!
                  </p>
                  <Button onClick={() => {
                    const tabsTrigger = document.querySelector('[value="search"]') as HTMLButtonElement;
                    tabsTrigger?.click();
                  }}>
                    <UserPlus className="h-4 w-4 mr-2" />
                    Find People
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-3">
                {connections.map((connection) => {
                  const friend = connection.requester.user_id === user.id 
                    ? connection.receiver 
                    : connection.requester;
                  
                  return (
                    <Card key={connection.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-lg font-semibold text-primary">
                                {friend.full_name.charAt(0)}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium">{friend.full_name}</p>
                              <p className="text-sm text-muted-foreground">@{friend.username}</p>
                              {friend.bio && (
                                <p className="text-xs text-muted-foreground mt-1">{friend.bio}</p>
                              )}
                              {friend.interests && friend.interests.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-2">
                                  {friend.interests.slice(0, 3).map((interest, idx) => (
                                    <Badge key={idx} variant="secondary" className="text-xs">
                                      {interest}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => navigate('/messages')}
                          >
                            Message
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </TabsContent>

          <TabsContent value="requests" className="space-y-4">
            {requests.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No pending requests</h3>
                  <p className="text-muted-foreground">
                    You'll see connection requests from other users here.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-3">
                {requests.map((request) => (
                  <Card key={request.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                            <span className="text-lg font-semibold text-primary">
                              {request.requester.full_name.charAt(0)}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium">{request.requester.full_name}</p>
                            <p className="text-sm text-muted-foreground">@{request.requester.username}</p>
                            {request.requester.bio && (
                              <p className="text-xs text-muted-foreground mt-1">{request.requester.bio}</p>
                            )}
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleConnectionRequest(request.id, 'reject')}
                          >
                            Decline
                          </Button>
                          <Button 
                            size="sm"
                            onClick={() => handleConnectionRequest(request.id, 'accept')}
                          >
                            Accept
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="search" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Search className="h-5 w-5" />
                  <span>Find People</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by name or username..."
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      searchUsers(e.target.value);
                    }}
                    className="pl-10"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Show search results if there's a query */}
            {searchQuery ? (
              loadingSearch ? (
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="animate-pulse">Searching...</div>
                  </CardContent>
                </Card>
              ) : searchResults.length > 0 ? (
                <div className="space-y-3">
                  {searchResults.map((profile) => (
                    <Card key={profile.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-lg font-semibold text-primary">
                                {profile.full_name.charAt(0)}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium">{profile.full_name}</p>
                              <p className="text-sm text-muted-foreground">@{profile.username}</p>
                              {profile.bio && (
                                <p className="text-xs text-muted-foreground mt-1">{profile.bio}</p>
                              )}
                              {profile.is_business && (
                                <Badge variant="outline" className="text-xs mt-1">
                                  Business
                                </Badge>
                              )}
                            </div>
                          </div>
                          <Button 
                            size="sm"
                            onClick={() => sendConnectionRequest(profile.user_id)}
                          >
                            <UserPlus className="h-4 w-4 mr-2" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No users found</h3>
                    <p className="text-muted-foreground">
                      Try searching with a different name or username.
                    </p>
                  </CardContent>
                </Card>
              )
            ) : (
              /* Show nearby people when not searching */
              <>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <MapPin className="h-5 w-5 text-primary" />
                      <span>People Nearby</span>
                    </CardTitle>
                  </CardHeader>
                </Card>
                
                {loadingNearby ? (
                  <Card>
                    <CardContent className="p-6 text-center">
                      <div className="animate-pulse flex items-center justify-center space-x-2">
                        <MapPin className="h-4 w-4 text-primary animate-bounce" />
                        <span>Finding people nearby...</span>
                      </div>
                    </CardContent>
                  </Card>
                ) : nearbyPeople.length > 0 ? (
                  <div className="space-y-3">
                    {nearbyPeople.map((profile) => (
                      <Card key={profile.id}>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                                <span className="text-lg font-semibold text-primary">
                                  {profile.full_name.charAt(0)}
                                </span>
                              </div>
                              <div>
                                <div className="flex items-center space-x-2">
                                  <p className="font-medium">{profile.full_name}</p>
                                  {profile.distance_km && (
                                    <Badge variant="secondary" className="text-xs">
                                      {profile.distance_km}km away
                                    </Badge>
                                  )}
                                </div>
                                <p className="text-sm text-muted-foreground">@{profile.username}</p>
                                {profile.bio && (
                                  <p className="text-xs text-muted-foreground mt-1">{profile.bio}</p>
                                )}
                                {profile.interests && profile.interests.length > 0 && (
                                  <div className="flex flex-wrap gap-1 mt-2">
                                    {profile.interests.slice(0, 3).map((interest, idx) => (
                                      <Badge key={idx} variant="outline" className="text-xs">
                                        {interest}
                                      </Badge>
                                    ))}
                                  </div>
                                )}
                                {profile.is_business && (
                                  <Badge variant="outline" className="text-xs mt-1">
                                    Business
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <Button 
                              size="sm"
                              onClick={() => sendConnectionRequest(profile.user_id)}
                            >
                              <UserPlus className="h-4 w-4 mr-2" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No people nearby</h3>
                      <p className="text-muted-foreground mb-4">
                        We couldn't find any people in your area right now.
                      </p>
                      <Button 
                        variant="outline" 
                        onClick={fetchNearbyPeople}
                        disabled={loadingNearby}
                      >
                        <MapPin className="h-4 w-4 mr-2" />
                        Refresh
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </>
            )}
          </TabsContent>
        </Tabs>
      </main>

      <MobileNavigation />
    </div>
  );
};

export default Connections;