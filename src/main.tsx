import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { toast } from '@/hooks/use-toast';

// Deep link handler for Supabase OAuth callback
import { App as CapacitorApp } from '@capacitor/app';
import { supabase } from '@/integrations/supabase/client';

CapacitorApp.addListener('appUrlOpen', async ({ url }) => {
    console.log('Deep link received:', url);

    if (url.includes('auth/callback')) {
        try {
            // Extract the URL fragment that contains the auth tokens
            const urlObj = new URL(url);
            const fragment = urlObj.hash || urlObj.search;

            console.log('Processing auth callback with fragment:', fragment);

            if (fragment) {
                // Parse the fragment to get access_token and refresh_token
                const params = new URLSearchParams(fragment.replace('#', ''));
                const accessToken = params.get('access_token');
                const refreshToken = params.get('refresh_token');

                if (accessToken) {
                    // Set the session using the tokens
                    const { data, error } = await supabase.auth.setSession({
                        access_token: accessToken,
                        refresh_token: refreshToken || '',
                    });

                    console.log('Session set result:', { data, error });

                    if (error) {
                        console.error('OAuth session error:', error.message);
                        toast({
                            title: 'Login failed',
                            description: error.message,
                            variant: 'destructive'
                        });
                    } else if (data?.session) {
                        console.log('OAuth login successful');
                        // Session established, navigate to home
                        window.location.replace('/');
                    }
                } else {
                    console.error('No access token found in callback URL');
                    toast({
                        title: 'Login failed',
                        description: 'No authentication token received.',
                        variant: 'destructive'
                    });
                }
            } else {
                console.error('No fragment found in callback URL');
                toast({
                    title: 'Login failed',
                    description: 'Invalid callback URL format.',
                    variant: 'destructive'
                });
            }
        } catch (err) {
            console.error('Error handling deep link:', err);
            toast({
                title: 'Login failed',
                description: 'Error processing authentication callback.',
                variant: 'destructive'
            });
        }
    }
});

createRoot(document.getElementById("root")!).render(<App />);
