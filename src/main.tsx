import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { toast } from '@/hooks/use-toast';

// Deep link handler for Supabase OAuth callback
import { App as CapacitorApp } from '@capacitor/app';
import { supabase } from '@/integrations/supabase/client';

CapacitorApp.addListener('appUrlOpen', async ({ url }) => {
    if (url.includes('auth/callback')) {
        try {
            const { data, error } = await supabase.auth.exchangeCodeForSession(url);
            console.log('exchangeCodeForSession result:', { data, error });
            if (error) {
                console.error('OAuth session exchange error:', error.message);
            }
            if (data?.session) {
                // Session established, reload or navigate
                window.location.replace('/');
            } else {
                // No session, show error or stay on auth
                toast({ title: 'Login failed', description: 'Could not establish session.' });
            }
        } catch (err) {
            console.error('Error handling deep link:', err);
        }
    }
});

createRoot(document.getElementById("root")!).render(<App />);
