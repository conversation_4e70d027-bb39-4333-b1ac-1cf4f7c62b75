import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useNavigate, useLocation } from "react-router-dom";
import { AuthProvider, useAuth, useLocationOnboarding } from "@/hooks/useAuth";
import LocationOnboarding from "@/components/LocationOnboarding";
import Index from "./pages/Home/Index";
import Auth from "./pages/Auth/Auth";
import Nearby from "./pages/Nearby/Nearby";
import Connections from "./pages/Connections/Connections";
import Messages from "./pages/Messages/Messages";
import Profile from "./pages/Profile/Profile";
import NotFound from "./pages/NotFound";
import { useState, useEffect } from "react";
import { Analytics } from "@vercel/analytics/react"
import CompleteProfile from "./pages/CompleteProfile/CompleteProfile";
import { useUserProfile } from "@/hooks/useUserProfile";
import { oauthHand<PERSON> } from "@/utils/oauthHandler";

const queryClient = new QueryClient();

// Component that handles location onboarding flow
const AppContent = () => {
  const { user, loading } = useAuth();
  const needsLocationOnboarding = useLocationOnboarding();
  const { profile, loading: profileLoading } = useUserProfile();
  const [onboardingCompleted, setOnboardingCompleted] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Initialize OAuth handler for mobile deep links
  useEffect(() => {
    oauthHandler.startListening();
    return () => oauthHandler.stopListening();
  }, []);

  // Show loading while checking auth, profile, and location status
  if (loading || profileLoading || needsLocationOnboarding === null) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse text-muted-foreground">Loading...</div>
      </div>
    );
  }

  // Redirect to /complete-profile if user is authenticated and missing username in profile
  if (user && profile && !profile.username && location.pathname !== "/complete-profile") {
    navigate("/complete-profile", { replace: true });
    return null;
  }

  // Show location onboarding for authenticated users who need it
  if (user && needsLocationOnboarding && !onboardingCompleted) {
    return (
      <LocationOnboarding
        onComplete={() => setOnboardingCompleted(true)}
        onSkip={() => setOnboardingCompleted(true)}
      />
    );
  }

  // Regular app routes
  return (
    <Routes>
      <Route path="/" element={<Index />} />
      <Route path="/auth" element={<Auth />} />
      <Route path="/nearby" element={<Nearby />} />
      <Route path="/connections" element={<Connections />} />
      <Route path="/messages" element={<Messages />} />
      <Route path="/profile" element={<Profile />} />
      <Route path="/complete-profile" element={<CompleteProfile />} />
      {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AuthProvider>
        <Toaster />
        <Sonner />
        <Analytics />
        <BrowserRouter>
          <AppContent />
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
