import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MapPin, Users, Shield, ArrowRight } from 'lucide-react';
import { useLocation } from '@/hooks/useLocation';
import LocationMap from './LocationMap';

interface LocationOnboardingProps {
  onComplete: () => void;
  onSkip: () => void;
}

const LocationOnboarding = ({ onComplete, onSkip }: LocationOnboardingProps) => {
  const [step, setStep] = useState<'intro' | 'map' | 'requesting' | 'success'>('intro');
  const { updateUserLocation, loading } = useLocation();

  const handleEnableLocation = () => {
    setStep('map');
  };

  const handleLocationConfirm = async (lat: number, lng: number) => {
    setStep('requesting');
    
    const success = await updateUserLocation(lat, lng);
    if (success) {
      setStep('success');
      setTimeout(() => {
        onComplete();
      }, 2000);
    } else {
      setStep('intro');
    }
  };

  const handleMapCancel = () => {
    setStep('intro');
  };

  // Show map for location selection
  if (step === 'map') {
    return (
      <LocationMap
        onLocationConfirm={handleLocationConfirm}
        onCancel={handleMapCancel}
      />
    );
  }

  // Show success screen
  if (step === 'success') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary/10 to-secondary/10 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="relative mb-6">
              <MapPin className="h-16 w-16 text-primary mx-auto" />
              <div className="absolute -top-2 -right-2 h-6 w-6 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm">✓</span>
              </div>
            </div>
            <h2 className="text-2xl font-bold mb-2">Location Enabled!</h2>
            <p className="text-muted-foreground">
              You can now discover and connect with interesting people nearby.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 to-secondary/10 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="relative mb-4">
            <MapPin className="h-12 w-12 text-primary mx-auto" />
            <div className="absolute -top-1 -right-1 h-4 w-4 bg-green-500 rounded-full pulse-location" />
          </div>
          <CardTitle className="text-2xl">Enable Location</CardTitle>
          <CardDescription>
            Discover and connect with interesting people around you
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Benefits */}
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <Users className="h-5 w-5 text-primary mt-1" />
              <div>
                <p className="font-medium">Find People Nearby</p>
                <p className="text-sm text-muted-foreground">
                  Discover like-minded people in your area
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <MapPin className="h-5 w-5 text-primary mt-1" />
              <div>
                <p className="font-medium">Location-Based Posts</p>
                <p className="text-sm text-muted-foreground">
                  See relevant content from your neighborhood
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <Shield className="h-5 w-5 text-primary mt-1" />
              <div>
                <p className="font-medium">Privacy Protected</p>
                <p className="text-sm text-muted-foreground">
                  Your exact location is never shared with others
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button 
              onClick={handleEnableLocation}
              disabled={loading || step === 'requesting'}
              className="w-full"
              size="lg"
            >
              {loading || step === 'requesting' ? (
                "Setting Up Location..."
              ) : (
                <>
                  Enable Location
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
            
            <Button 
              variant="ghost" 
              onClick={onSkip}
              className="w-full"
              disabled={loading || step === 'requesting'}
            >
              Skip for Now
            </Button>
          </div>

          <p className="text-xs text-muted-foreground text-center">
            You can enable location later in your profile settings
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default LocationOnboarding;