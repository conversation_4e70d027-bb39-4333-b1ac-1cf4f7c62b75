import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { MapPin, Target, Check, Navigation } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { GoogleMap, Marker, useLoadScript } from '@react-google-maps/api';

interface LocationMapProps {
  onLocationConfirm: (lat: number, lng: number) => void;
  onCancel: () => void;
  initialLocation?: { lat: number; lng: number };
}

const mapContainerStyle = { width: '100vw', height: '71.75vh' };

const customMapStyle = [
  // Hide roads
  { featureType: "road", elementType: "geometry", stylers: [{ visibility: "off" }] },
  { featureType: "road", elementType: "labels", stylers: [{ visibility: "off" }] },
  // Hide businesses
  { featureType: "poi.business", stylers: [{ visibility: "off" }] },
  // Hide transit
  { featureType: "transit", stylers: [{ visibility: "off" }] },
  // Show only place names and areas
  { featureType: "poi", elementType: "labels.text", stylers: [{ visibility: "on" }] },
  { featureType: "administrative", elementType: "labels.text", stylers: [{ visibility: "on" }] },
];


const LocationMap = ({ onLocationConfirm, onCancel, initialLocation }: LocationMapProps) => {
  const [currentLocation, setCurrentLocation] = useState<{ lat: number; lng: number } | null>(
    initialLocation || null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [locationPermissionDenied, setLocationPermissionDenied] = useState(false);

  const { isLoaded: mapsLoaded } = useLoadScript({
    googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
  });

  // Continuously request location updates unless permission denied
  useEffect(() => {
    let watchId: number | null = null;
    if (navigator.geolocation && !locationPermissionDenied) {
      setIsLoading(true);
      watchId = navigator.geolocation.watchPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setCurrentLocation({ lat: latitude, lng: longitude });
          setIsLoading(false);
        },
        (error) => {
          if (error.code === error.PERMISSION_DENIED) {
            setLocationPermissionDenied(true);
          }
          setIsLoading(false);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 1000,
        }
      );
    }
    return () => {
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
      }
    };
  }, [locationPermissionDenied]);

  // Show loading if no API key yet
  if (!mapsLoaded) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mx-auto"></div>
          <p className="text-muted-foreground">Loading map...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <div className="bg-card border-b border-border p-4 z-10 relative">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold">Set Your Location</h2>
            <p className="text-sm text-muted-foreground">
              Tap on the map or drag the pin to set your location
            </p>
          </div>
          <Button
            onClick={() => {
              if (navigator.geolocation) {
                setIsLoading(true);
                navigator.geolocation.getCurrentPosition(
                  (position) => {
                    const { latitude, longitude } = position.coords;
                    setCurrentLocation({ lat: latitude, lng: longitude });
                    setIsLoading(false);
                  },
                  (error) => {
                    if (error.code === error.PERMISSION_DENIED) {
                      setLocationPermissionDenied(true);
                    }
                    setIsLoading(false);
                  },
                  {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 1000,
                  }
                );
              }
            }}
            variant="outline"
            size="sm"
            className="flex items-center space-x-2"
            disabled={isLoading || locationPermissionDenied}
          >
            <Navigation className="h-4 w-4" />
            <span>{isLoading ? "Finding..." : locationPermissionDenied ? "Location Blocked" : "My Location"}</span>
          </Button>
        </div>
      </div>

      {/* Google Map Container */}
      <div className="flex-1 relative bg-muted">
        <GoogleMap
          mapContainerStyle={mapContainerStyle}
          zoom={currentLocation ? 16 : 12}
          center={currentLocation || { lat: 28.6139, lng: 77.2090 }}
          options={{
            styles: customMapStyle,
            disableDefaultUI: true,
            zoomControl: true,
            streetViewControl: false,
            mapTypeControl: false,
            clickableIcons: false,
          }}
          onClick={(e) => {
            if (e.latLng) {
              setCurrentLocation({ lat: e.latLng.lat(), lng: e.latLng.lng() });
            }
          }}
        >
          {currentLocation && (
            <Marker
              position={currentLocation}
              draggable
              onDragEnd={(e) => {
                setCurrentLocation({ lat: e.latLng.lat(), lng: e.latLng.lng() });
              }}
            />
          )}
        </GoogleMap>

        {/* Center crosshair for better UX */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10 pointer-events-none">
          <div className="w-8 h-8 flex items-center justify-center">
            <div className="w-1 h-1 bg-primary rounded-full"></div>
            <div className="absolute w-6 h-0.5 bg-primary/60"></div>
            <div className="absolute w-0.5 h-6 bg-primary/60"></div>
          </div>
        </div>

        {/* Location info overlay */}
        {currentLocation && (
          <div className="absolute top-4 left-4 right-4 z-10">
            <Card className="bg-card/95 backdrop-blur-sm">
              <CardContent className="p-3">
                <div className="flex items-center space-x-2 text-sm">
                  <MapPin className="h-4 w-4 text-primary" />
                  <span className="font-medium">Location:</span>
                  <span className="text-muted-foreground truncate">
                    {currentLocation.lat.toFixed(5)}, {currentLocation.lng.toFixed(5)}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Bottom actions - ride-sharing style */}
      <div className="bg-card border-t border-border p-4 space-y-4">
        <div className="text-center">
          <p className="text-sm font-medium mb-1">
            {currentLocation ? "Location Selected" : "Choose your location"}
          </p>
          <p className="text-xs text-muted-foreground">
            Your exact location stays private. Only your general area is shown to others.
          </p>
        </div>

        <div className="flex space-x-3">
          <Button onClick={onCancel} variant="outline" className="flex-1" size="lg">
            Cancel
          </Button>
          <Button
            onClick={() => {
              if (currentLocation) {
                onLocationConfirm(currentLocation.lat, currentLocation.lng);
              }
            }}
            disabled={!currentLocation || isLoading}
            className="flex-1"
            size="lg"
          >
            <Check className="h-4 w-4 mr-2" />
            Confirm Location
          </Button>
        </div>
      </div>
    </div>
  );
};

export default LocationMap;