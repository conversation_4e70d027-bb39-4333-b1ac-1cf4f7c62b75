# Google OAuth Setup Guide for Nearby Connect

## Issues Fixed (Latest Update)

1. **Switched to web-based OAuth flow** - Now uses proper web redirect instead of custom scheme
2. **Added OAuth callback handler** - Handles OAuth tokens from URL hash/query parameters
3. **Enhanced deep link support** - Added both custom scheme and web URL deep links
4. **Improved session management** - Better handling of OAuth session establishment
5. **Added dedicated OAuth handler** - Centralized OAuth processing with proper error handling

## Required Supabase Configuration

### 1. Google OAuth Provider Setup

In your Supabase dashboard:

1. Go to **Authentication** > **Providers**
2. Enable **Google** provider
3. Configure the following settings:

**Client ID**: Your Google OAuth Client ID
**Client Secret**: Your Google OAuth Client Secret

### 2. Redirect URLs Configuration

Add these redirect URLs in your Google Cloud Console OAuth settings:

**For Supabase (Required):**

- `https://gjerzbealtpbfgonigby.supabase.co/auth/v1/callback`

**For Mobile App (Required):**

- `https://nearbyconnect-v004.vercel.app/auth`
- `nearbyconnect://auth/callback` (backup custom scheme)

### 3. Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create or select your project
3. Enable **Google+ API** and **Google Identity API**
4. Go to **Credentials** > **OAuth 2.0 Client IDs**
5. Create credentials for:
   - **Web application** (for Supabase)
   - **Android** (for mobile app)

**Android OAuth Client:**
- Package name: `com.rawatinnovations.nearbyconnect`
- SHA-1 certificate fingerprint: (Get from your keystore)

## Testing the OAuth Flow

### 1. Check Console Logs

When testing, check the browser/device console for these logs:
- "Deep link received: [URL]"
- "Processing auth callback with fragment: [fragment]"
- "Session set result: [result]"
- "OAuth login successful"

### 2. Verify Supabase Dashboard

After successful login, check:
- **Authentication** > **Users** - New user should appear
- **Table Editor** > **profiles** - Profile should be created

### 3. Common Issues and Solutions

**Issue**: "No access token found in callback URL"
**Solution**: Check Google OAuth configuration and redirect URLs

**Issue**: "Invalid callback URL format"
**Solution**: Verify AndroidManifest.xml deep link configuration

**Issue**: User stays on auth screen after redirect
**Solution**: Check auth state listener and session handling

## Mobile-Specific Configuration

### AndroidManifest.xml
The intent filter is already configured correctly:
```xml
<intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="nearbyconnect" android:host="auth" android:path="/callback" />
</intent-filter>
```

### Capacitor Configuration
Updated to include proper app launch URL handling.

## Next Steps

1. **Configure Google OAuth in Supabase Dashboard**
2. **Set up Google Cloud Console credentials**
3. **Test the OAuth flow**
4. **Monitor console logs for debugging**
5. **Verify user creation in Supabase**

## Debugging Commands

```bash
# Check Android app logs
adb logcat | grep -i oauth

# Check Capacitor logs
npx cap run android --livereload --external

# Monitor Supabase logs
# Check the Supabase dashboard logs section
```
