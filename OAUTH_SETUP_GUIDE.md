# Google OAuth Setup Guide for Nearby Connect

## Issues Fixed

1. **Improved OAuth redirect handling** - Updated the deep link handler to properly parse OAuth tokens
2. **Enhanced error handling** - Added comprehensive error logging and user feedback
3. **Better auth state management** - Improved session handling and state updates
4. **Debugging support** - Added console logs to track the authentication flow

## Required Supabase Configuration

### 1. Google OAuth Provider Setup

In your Supabase dashboard:

1. Go to **Authentication** > **Providers**
2. Enable **Google** provider
3. Configure the following settings:

**Client ID**: Your Google OAuth Client ID
**Client Secret**: Your Google OAuth Client Secret

### 2. Redirect URLs Configuration

Add these redirect URLs in your Google Cloud Console OAuth settings:

**For Development:**
- `https://gjerzbealtpbfgonigby.supabase.co/auth/v1/callback`

**For Production:**
- `https://gjerzbealtpbfgonigby.supabase.co/auth/v1/callback`

**For Mobile (Custom Scheme):**
- `nearbyconnect://auth/callback`

### 3. Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create or select your project
3. Enable **Google+ API** and **Google Identity API**
4. Go to **Credentials** > **OAuth 2.0 Client IDs**
5. Create credentials for:
   - **Web application** (for Supabase)
   - **Android** (for mobile app)

**Android OAuth Client:**
- Package name: `com.rawatinnovations.nearbyconnect`
- SHA-1 certificate fingerprint: (Get from your keystore)

## Testing the OAuth Flow

### 1. Check Console Logs

When testing, check the browser/device console for these logs:
- "Deep link received: [URL]"
- "Processing auth callback with fragment: [fragment]"
- "Session set result: [result]"
- "OAuth login successful"

### 2. Verify Supabase Dashboard

After successful login, check:
- **Authentication** > **Users** - New user should appear
- **Table Editor** > **profiles** - Profile should be created

### 3. Common Issues and Solutions

**Issue**: "No access token found in callback URL"
**Solution**: Check Google OAuth configuration and redirect URLs

**Issue**: "Invalid callback URL format"
**Solution**: Verify AndroidManifest.xml deep link configuration

**Issue**: User stays on auth screen after redirect
**Solution**: Check auth state listener and session handling

## Mobile-Specific Configuration

### AndroidManifest.xml
The intent filter is already configured correctly:
```xml
<intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="nearbyconnect" android:host="auth" android:path="/callback" />
</intent-filter>
```

### Capacitor Configuration
Updated to include proper app launch URL handling.

## Next Steps

1. **Configure Google OAuth in Supabase Dashboard**
2. **Set up Google Cloud Console credentials**
3. **Test the OAuth flow**
4. **Monitor console logs for debugging**
5. **Verify user creation in Supabase**

## Debugging Commands

```bash
# Check Android app logs
adb logcat | grep -i oauth

# Check Capacitor logs
npx cap run android --livereload --external

# Monitor Supabase logs
# Check the Supabase dashboard logs section
```
